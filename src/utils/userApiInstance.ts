import { User<PERSON><PERSON> } from "./apis/generated/user_api";

// Create a singleton instance of UserAPI for authentication
// Using production environment and account key to match the curl example
export const userAPIClient = new UserAPI({
	baseURL: "https://users-service.onrewind.tv",
	headers: {
		Accept: "application/json",
		"Accept-Charset": "UTF-8",
		"Content-Type": "application/json",
		"x-account-key": "ByAWCu-i5", // Updated to match the curl example
		"User-Agent": "ktor-client",
		"Accept-Language": "en-GB,en;q=0.9",
	},
});

// Set authentication token for user API requests
export const setUserApiAuthToken = (token: string | null) => {
	userAPIClient.instance.defaults.headers.common["Authorization"] =
		token ? `Bearer ${token}` : "";
};
