import { User<PERSON><PERSON> } from "./apis/generated/user_api";

// Create a singleton instance of UserAPI for authentication
// Using dev environment and account key to match mobile implementation
export const userAPIClient = new UserAPI({
	baseURL: "https://dev-users-service.onrewind.tv",
	headers: {
		Accept: "application/json",
		"Accept-Charset": "UTF-8",
		"Content-Type": "application/json",
		"x-account-key": "r1x-ei_zq", // Updated to match mobile implementation
		"User-Agent": "ktor-client",
		"Accept-Language": "en-GB,en;q=0.9",
	},
});

// Set authentication token for user API requests
export const setUserApiAuthToken = (token: string | null) => {
	userAPIClient.instance.defaults.headers.common["Authorization"] =
		token ? `Bearer ${token}` : "";
};
