import { User<PERSON><PERSON> } from "./apis/generated/user_api";

// Create a singleton instance of UserAPI for authentication
// Using the same endpoint as the web version
export const userAPIClient = new UserAPI({
	baseURL: "https://api-gateway.onrewind.tv/users-service-api",
	headers: {
		Accept: "*/*",
		"Accept-Charset": "UTF-8",
		"Content-Type": "application/json",
		"x-account-key": "ByAWCu-i5", // Updated to match the web version
		"User-Agent":
			"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
		"Accept-Language": "en-US,en;q=0.9",
	},
});

// Set authentication token for user API requests
export const setUserApiAuthToken = (token: string | null) => {
	userAPIClient.instance.defaults.headers.common["Authorization"] =
		token ? `Bearer ${token}` : "";
};
