import { userAPIClient } from "../utils/userApiInstance";
import { setAuthToken } from "../utils/kenticoInstance";
import { setMainApiAuthToken } from "../utils/mainApiInstance";
import { setUserApiAuthToken } from "../utils/userApiInstance";
import { TokenStorage, TokenData } from "./tokenStorage";
import {
	FirebaseAuthSimple,
	FirebaseAuthError,
} from "./firebaseAuthSimple";

// Interface for login credentials
export interface LoginCredentials {
	email: string;
	password: string;
}

// Interface for login response
export interface LoginResponse {
	success: boolean;
	accessToken?: string;
	refreshToken?: string;
	userId?: string;
	expiresIn?: number;
	error?: string;
}

// Interface for API login response (matches the actual API response)
interface ApiLoginResponse {
	id: string;
	accessToken: string;
	refreshToken: string;
	tokenType: string;
	expiresIn: number;
	action: string;
}

/**
 * Authentication Service
 * Handles complete authentication flow with Firebase and token management
 */
export class AuthService {
	/**
	 * Login with email and password
	 * 1. Checks if user exists (GET /platform/fans)
	 * 2. Authenticates with Firebase using email/password
	 * 3. Exchanges Firebase token for app access token
	 * 4. Stores tokens securely
	 * 5. Sets tokens in API clients
	 */
	static async login(
		credentials: LoginCredentials
	): Promise<LoginResponse> {
		try {
			console.log("Starting login process for:", credentials.email);

			// Step 1: Check if user exists (matching mobile implementation)
			console.log("Checking if user exists...");
			try {
				await userAPIClient.platform.getFanByEmail({
					email: credentials.email,
				});
				console.log("User exists, proceeding with login");
			} catch (error: any) {
				// If user doesn't exist (404), we can still proceed with login
				// as the login process might create the user
				if (error.response?.status === 404) {
					console.log(
						"User not found, but proceeding with login (user might be created)"
					);
				} else {
					console.log(
						"User check failed, but proceeding with login:",
						error.message
					);
				}
			}

			// Step 2: Authenticate with Firebase
			const firebaseResult =
				await FirebaseAuthSimple.signInWithEmailAndPassword(
					credentials.email,
					credentials.password
				);

			console.log("Firebase authentication successful");
			console.log(
				"Firebase token (first 50 chars):",
				firebaseResult.token.substring(0, 50) + "..."
			);

			// Step 3: Exchange Firebase token for app access token
			const loginPayload = {
				vendorSSO: {
					name: "firebase" as const,
					token: firebaseResult.token,
				},
			};

			console.log("Sending login request with payload:", {
				vendorSSO: {
					name: loginPayload.vendorSSO.name,
					token:
						loginPayload.vendorSSO.token.substring(0, 50) + "...",
				},
			});

			console.log(
				"Request headers:",
				userAPIClient.instance.defaults.headers
			);

			const response = await userAPIClient.auth.loginFan(
				loginPayload
			);

			// Step 4: Process the API response
			if (response.data && this.isValidApiResponse(response.data)) {
				const apiResponse = response.data as ApiLoginResponse;

				console.log("API login successful:", {
					userId: apiResponse.id,
					tokenType: apiResponse.tokenType,
					expiresIn: apiResponse.expiresIn,
					action: apiResponse.action,
				});

				// Step 5: Store tokens securely
				const tokenData: TokenData = {
					accessToken: apiResponse.accessToken,
					refreshToken: apiResponse.refreshToken,
					expiresIn: apiResponse.expiresIn,
					userId: apiResponse.id,
				};

				await TokenStorage.storeTokens(tokenData);

				// Step 6: Set tokens in API clients
				this.setAuthTokens(apiResponse.accessToken);

				console.log("✅ Login completed successfully!");
				console.log(
					"📱 User authenticated and tokens set in API clients"
				);

				return {
					success: true,
					accessToken: apiResponse.accessToken,
					refreshToken: apiResponse.refreshToken,
					userId: apiResponse.id,
					expiresIn: apiResponse.expiresIn,
				};
			} else {
				console.error("Invalid API response format:", response.data);
				return {
					success: false,
					error: "Login failed: Invalid response from server",
				};
			}
		} catch (error: any) {
			console.error("Login error:", error);

			// Handle Firebase authentication errors
			if (error.code) {
				const firebaseError = error as FirebaseAuthError;
				return {
					success: false,
					error: firebaseError.message, // FirebaseAuthSimple already provides readable messages
				};
			}

			// Handle API errors
			let errorMessage = "Login failed";
			if (error.response?.data?.message) {
				errorMessage = error.response.data.message;
			} else if (error.message) {
				errorMessage = error.message;
			}

			return {
				success: false,
				error: errorMessage,
			};
		}
	}

	/**
	 * Refresh access token using stored refresh token
	 */
	static async refreshToken(): Promise<LoginResponse> {
		try {
			const refreshToken = await TokenStorage.getRefreshToken();

			if (!refreshToken) {
				return {
					success: false,
					error: "No refresh token available",
				};
			}

			console.log("Refreshing access token...");

			// Call refresh token API
			const response = await userAPIClient.auth.refreshTokenFan({
				refreshToken: refreshToken,
			});

			if (response.data && this.isValidApiResponse(response.data)) {
				const apiResponse = response.data as ApiLoginResponse;

				console.log("Token refresh successful");

				// Store new tokens
				const tokenData: TokenData = {
					accessToken: apiResponse.accessToken,
					refreshToken: apiResponse.refreshToken,
					expiresIn: apiResponse.expiresIn,
					userId: apiResponse.id,
				};

				await TokenStorage.storeTokens(tokenData);

				// Set new access token in API clients
				this.setAuthTokens(apiResponse.accessToken);

				return {
					success: true,
					accessToken: apiResponse.accessToken,
					refreshToken: apiResponse.refreshToken,
					userId: apiResponse.id,
					expiresIn: apiResponse.expiresIn,
				};
			} else {
				console.error(
					"Invalid refresh response format:",
					response.data
				);
				return {
					success: false,
					error: "Token refresh failed: Invalid response from server",
				};
			}
		} catch (error: any) {
			console.error("Token refresh error:", error);

			let errorMessage = "Token refresh failed";
			if (error.response?.data?.message) {
				errorMessage = error.response.data.message;
			} else if (error.message) {
				errorMessage = error.message;
			}

			return {
				success: false,
				error: errorMessage,
			};
		}
	}

	/**
	 * Get current access token, refreshing if necessary
	 */
	static async getValidAccessToken(): Promise<string | null> {
		try {
			// Check if current token is expired
			const isExpired = await TokenStorage.isTokenExpired();

			if (isExpired) {
				console.log("Access token expired, attempting refresh...");
				const refreshResult = await this.refreshToken();

				if (refreshResult.success && refreshResult.accessToken) {
					return refreshResult.accessToken;
				} else {
					console.log(
						"Token refresh failed, user needs to login again"
					);
					await this.logout();
					return null;
				}
			}

			// Return current valid token
			return await TokenStorage.getAccessToken();
		} catch (error) {
			console.error("Error getting valid access token:", error);
			return null;
		}
	}

	/**
	 * Logout user and clear all tokens
	 */
	static async logout(): Promise<void> {
		try {
			console.log("Logging out user...");

			// Clear stored tokens
			await TokenStorage.clearTokens();

			// Clear tokens from API clients
			this.clearAuthTokens();

			// Note: With simple Firebase auth, we don't need to explicitly sign out
			// The token will simply expire or be cleared from storage

			console.log("Logout successful");
		} catch (error) {
			console.error("Error during logout:", error);
		}
	}

	/**
	 * Check if user is currently authenticated
	 */
	static async isAuthenticated(): Promise<boolean> {
		try {
			const accessToken = await TokenStorage.getAccessToken();
			const isExpired = await TokenStorage.isTokenExpired();
			return accessToken !== null && !isExpired;
		} catch (error) {
			console.error("Error checking authentication status:", error);
			return false;
		}
	}

	/**
	 * Initialize authentication on app start
	 * Checks for stored tokens and sets them in API clients
	 */
	static async initializeAuth(): Promise<boolean> {
		try {
			const accessToken = await this.getValidAccessToken();

			if (accessToken) {
				this.setAuthTokens(accessToken);
				console.log("Authentication initialized with valid token");
				return true;
			} else {
				console.log("No valid authentication found");
				return false;
			}
		} catch (error) {
			console.error("Error initializing authentication:", error);
			return false;
		}
	}

	/**
	 * Validate API response structure
	 */
	private static isValidApiResponse(data: any): boolean {
		return (
			data &&
			typeof data === "object" &&
			"id" in data &&
			"accessToken" in data &&
			"refreshToken" in data &&
			"tokenType" in data &&
			"expiresIn" in data
		);
	}

	/**
	 * Set authentication tokens in all API instances
	 */
	private static setAuthTokens(accessToken: string): void {
		setAuthToken(accessToken);
		setMainApiAuthToken(accessToken);
		setUserApiAuthToken(accessToken);
	}

	/**
	 * Clear authentication tokens from all API instances
	 */
	private static clearAuthTokens(): void {
		setAuthToken(null);
		setMainApiAuthToken(null);
		setUserApiAuthToken(null);
	}

	/**
	 * Basic email validation
	 */
	static validateEmail(email: string): boolean {
		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
		return emailRegex.test(email);
	}

	/**
	 * Basic password validation
	 */
	static validatePassword(password: string): boolean {
		return password.length >= 6; // Minimum 6 characters
	}

	/**
	 * Legacy method for backward compatibility
	 * Redirects to the new login method
	 */
	static async loginWithFirebase(
		credentials: LoginCredentials
	): Promise<LoginResponse> {
		return this.login(credentials);
	}
}
