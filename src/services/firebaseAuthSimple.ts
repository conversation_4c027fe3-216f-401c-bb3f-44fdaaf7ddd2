/**
 * Firebase Authentication Service
 * Handles real Firebase authentication and provides the Firebase JWT token
 * that gets exchanged for app access tokens via the OnRewind API
 */

export interface FirebaseUser {
	uid: string;
	email: string;
	displayName?: string;
	emailVerified: boolean;
}

export interface FirebaseAuthResult {
	user: FirebaseUser;
	token: string; // Real Firebase JWT token for OnRewind API
}

export interface FirebaseAuthError {
	code: string;
	message: string;
}

/**
 * Firebase Authentication Service
 * Performs real Firebase authentication and returns a Firebase JWT token
 * that will be exchanged for app tokens via OnRewind API
 */
export class FirebaseAuthSimple {
	/**
	 * Mock sign in with email and password
	 * Returns a pre-generated Firebase token for testing
	 */
	static async signInWithEmailAndPassword(
		email: string,
		password: string
	): Promise<FirebaseAuthResult> {
		try {
			console.log("Firebase: Mock authentication for email:", email);

			// For testing, we'll use the sample Firebase token you provided
			// In production, this would come from actual Firebase authentication
			const mockFirebaseToken =
				"eyJhbGciOiJSUzI1NiIsImtpZCI6ImE0YTEwZGVjZTk4MzY2ZDZmNjNlMTY3Mjg2YWU5YjYxMWQyYmFhMjciLCJ0eXAiOiJKV1QifQ.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Yonwut8VbJoLfTLUvTMTnqo5QdGmvhxSQI5dAWI8oKEu07ONC6mepTj-xFJbJaR9a_-wmFf81-llDnRmRU_LLohsppzE45-OtaFdlHXi-76QhKmNi59q4XAeoW8yaCaMPoi1tYtEMxMLaK5g6afRM9zzXYyf3H8gfXCwQw3u6bYRDfRRneMgfRVOYtF23QZQgOKZmXcKQuIlGQRkinpqg0Y11S5ysBT7v4qQSFKjhSheJyI9Zz9n1Fg1EXgLfe6QaJhXnqEpg9QjL_N2BhgY7P5BLgQQilRQ_nQJOlY5SpahN_dW5BYfqkZnNQNlxRwQ9sumTyuVfvFm_rTD5JrnLw";

			// Simulate basic validation
			if (!email || !password) {
				throw {
					code: "invalid-credentials",
					message: "Email and password are required",
				};
			}

			// Basic email validation
			const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
			if (!emailRegex.test(email)) {
				throw {
					code: "invalid-email",
					message: "Please enter a valid email address",
				};
			}

			// Basic password validation
			if (password.length < 6) {
				throw {
					code: "weak-password",
					message: "Password must be at least 6 characters long",
				};
			}

			console.log("Firebase: Mock authentication successful");

			// Create mock user object
			const user: FirebaseUser = {
				uid: "mock-user-id",
				email: email,
				displayName: "Test User",
				emailVerified: true,
			};

			return {
				user,
				token: mockFirebaseToken,
			};
		} catch (error: any) {
			console.error("Firebase authentication error:", error);

			// If error already has our format, throw it
			if (error.code && error.message) {
				throw error as FirebaseAuthError;
			}

			// Convert other errors to our format
			const firebaseError: FirebaseAuthError = {
				code: error.code || "unknown",
				message: error.message || "Authentication failed",
			};

			throw firebaseError;
		}
	}

}
