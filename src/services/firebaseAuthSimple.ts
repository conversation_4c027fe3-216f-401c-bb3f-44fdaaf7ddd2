/**
 * Simple Firebase Authentication Service
 * Uses Firebase REST API instead of SDK for lighter implementation
 */

export interface FirebaseUser {
	uid: string;
	email: string;
	displayName?: string;
	emailVerified: boolean;
}

export interface FirebaseAuthResult {
	user: FirebaseUser;
	token: string; // Firebase JWT token
}

export interface FirebaseAuthError {
	code: string;
	message: string;
}

// Firebase project configuration - using environment variables
const FIREBASE_CONFIG = {
	apiKey: process.env.FIREBASE_API_KEY || "AIzaSyBl7_w7sRt8pVgE7Rx9yFX6uDdGvE9qCkI", // Replace with actual API key
	projectId: process.env.FIREBASE_PROJECT_ID || "white-label-da752", // Your Firebase project ID
};

// Validate Firebase configuration
if (!FIREBASE_CONFIG.apiKey || FIREBASE_CONFIG.apiKey === "your_firebase_api_key_here") {
	console.warn("⚠️  Firebase API key not configured. Please set FIREBASE_API_KEY in your environment variables.");
}

if (!FIREBASE_CONFIG.projectId) {
	console.warn("⚠️  Firebase project ID not configured. Please set FIREBASE_PROJECT_ID in your environment variables.");
}

/**
 * Simple Firebase Authentication Service
 * Uses REST API calls instead of full Firebase SDK
 */
export class FirebaseAuthSimple {
	/**
	 * Sign in with email and password using Firebase REST API
	 */
	static async signInWithEmailAndPassword(
		email: string,
		password: string
	): Promise<FirebaseAuthResult> {
		try {
			console.log("Firebase: Attempting to sign in with email:", email);

			// Firebase REST API endpoint for email/password authentication
			const signInUrl = `https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key=${FIREBASE_CONFIG.apiKey}`;

			const response = await fetch(signInUrl, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					email: email,
					password: password,
					returnSecureToken: true,
				}),
			});

			const data = await response.json();

			if (!response.ok) {
				throw {
					code: data.error?.message || "unknown",
					message: this.getReadableErrorMessage(
						data.error?.message || "unknown"
					),
				};
			}

			if (!data.idToken) {
				throw {
					code: "no-token",
					message: "No ID token received from Firebase",
				};
			}

			console.log("Firebase: Sign in successful for user:", data.localId);

			// Create user object from response
			const user: FirebaseUser = {
				uid: data.localId,
				email: data.email || email,
				displayName: data.displayName,
				emailVerified: data.emailVerified || false,
			};

			return {
				user,
				token: data.idToken,
			};
		} catch (error: any) {
			console.error("Firebase authentication error:", error);

			// If error already has our format, throw it
			if (error.code && error.message) {
				throw error as FirebaseAuthError;
			}

			// Convert other errors to our format
			const firebaseError: FirebaseAuthError = {
				code: error.code || "unknown",
				message: error.message || "Authentication failed",
			};

			throw firebaseError;
		}
	}

	/**
	 * Convert Firebase error codes to user-friendly messages
	 */
	private static getReadableErrorMessage(errorCode: string): string {
		switch (errorCode) {
			case "EMAIL_NOT_FOUND":
				return "No account found with this email address.";
			case "INVALID_PASSWORD":
				return "Incorrect password. Please try again.";
			case "INVALID_EMAIL":
				return "Please enter a valid email address.";
			case "USER_DISABLED":
				return "This account has been disabled.";
			case "TOO_MANY_ATTEMPTS_TRY_LATER":
				return "Too many failed attempts. Please try again later.";
			case "WEAK_PASSWORD":
				return "Password is too weak. Please choose a stronger password.";
			case "EMAIL_EXISTS":
				return "An account with this email already exists.";
			case "OPERATION_NOT_ALLOWED":
				return "Email/password authentication is not enabled.";
			default:
				return "Authentication failed. Please try again.";
		}
	}

	/**
	 * Create account with email and password (optional for future use)
	 */
	static async createUserWithEmailAndPassword(
		email: string,
		password: string
	): Promise<FirebaseAuthResult> {
		try {
			console.log("Firebase: Creating account for email:", email);

			const signUpUrl = `https://identitytoolkit.googleapis.com/v1/accounts:signUp?key=${FIREBASE_CONFIG.apiKey}`;

			const response = await fetch(signUpUrl, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					email: email,
					password: password,
					returnSecureToken: true,
				}),
			});

			const data = await response.json();

			if (!response.ok) {
				throw {
					code: data.error?.message || "unknown",
					message: this.getReadableErrorMessage(
						data.error?.message || "unknown"
					),
				};
			}

			console.log("Firebase: Account created for user:", data.localId);

			const user: FirebaseUser = {
				uid: data.localId,
				email: data.email || email,
				displayName: data.displayName,
				emailVerified: data.emailVerified || false,
			};

			return {
				user,
				token: data.idToken,
			};
		} catch (error: any) {
			console.error("Firebase account creation error:", error);

			if (error.code && error.message) {
				throw error as FirebaseAuthError;
			}

			const firebaseError: FirebaseAuthError = {
				code: error.code || "unknown",
				message: error.message || "Account creation failed",
			};

			throw firebaseError;
		}
	}
}
