/**
 * Firebase Authentication Service
 * Handles real Firebase authentication and provides the Firebase JWT token
 * that gets exchanged for app access tokens via the OnRewind API
 */

export interface FirebaseUser {
	uid: string;
	email: string;
	displayName?: string;
	emailVerified: boolean;
}

export interface FirebaseAuthResult {
	user: FirebaseUser;
	token: string; // Real Firebase JWT token for OnRewind API
}

export interface FirebaseAuthError {
	code: string;
	message: string;
}

// Firebase configuration for real authentication
const FIREBASE_CONFIG = {
	apiKey: "AIzaSyBl7_w7sRt8pVgE7Rx9yFX6uDdGvE9qCkI", // Firebase API key for white-label-da752
	projectId: "white-label-da752", // Firebase project ID from your curl example
};

/**
 * Firebase Authentication Service
 * Performs real Firebase authentication and returns a Firebase JWT token
 * that will be exchanged for app tokens via OnRewind API
 */
export class FirebaseAuth {
	/**
	 * Real Firebase authentication with email and password
	 * Uses Firebase REST API to get a dynamic JWT token
	 * Equivalent to Firebase.auth.currentUser?.getIdToken(true)
	 */
	static async signInWithEmailAndPassword(
		email: string,
		password: string
	): Promise<FirebaseAuthResult> {
		try {
			console.log(
				"Firebase: Attempting real authentication for email:",
				email
			);

			// Basic validation
			if (!email || !password) {
				throw {
					code: "invalid-credentials",
					message: "Email and password are required",
				};
			}

			// Basic email validation
			const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
			if (!emailRegex.test(email)) {
				throw {
					code: "invalid-email",
					message: "Please enter a valid email address",
				};
			}

			// Basic password validation
			if (password.length < 6) {
				throw {
					code: "weak-password",
					message: "Password must be at least 6 characters long",
				};
			}

			// Firebase REST API endpoint for email/password authentication
			const signInUrl = `https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key=${FIREBASE_CONFIG.apiKey}`;

			console.log(
				"Firebase: Making authentication request to Firebase..."
			);

			const response = await fetch(signInUrl, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					email: email,
					password: password,
					returnSecureToken: true,
				}),
			});

			const data = await response.json();

			if (!response.ok) {
				console.error("Firebase authentication failed:", data);
				throw {
					code: data.error?.message || "unknown",
					message: this.getReadableErrorMessage(
						data.error?.message || "unknown"
					),
				};
			}

			if (!data.idToken) {
				throw {
					code: "no-token",
					message: "No ID token received from Firebase",
				};
			}

			console.log(
				"Firebase: Authentication successful for user:",
				data.localId
			);
			console.log(
				"Firebase: Dynamic token received (first 50 chars):",
				data.idToken.substring(0, 50) + "..."
			);

			// Create user object from Firebase response
			const user: FirebaseUser = {
				uid: data.localId,
				email: data.email || email,
				displayName: data.displayName,
				emailVerified: data.emailVerified || false,
			};

			return {
				user,
				token: data.idToken, // This is the dynamic Firebase JWT token
			};
		} catch (error: any) {
			console.error("Firebase authentication error:", error);

			// If error already has our format, throw it
			if (error.code && error.message) {
				throw error as FirebaseAuthError;
			}

			// Convert other errors to our format
			const firebaseError: FirebaseAuthError = {
				code: error.code || "unknown",
				message: error.message || "Authentication failed",
			};

			throw firebaseError;
		}
	}

	/**
	 * Convert Firebase error codes to user-friendly messages
	 */
	private static getReadableErrorMessage(errorCode: string): string {
		switch (errorCode) {
			case "EMAIL_NOT_FOUND":
				return "No account found with this email address.";
			case "INVALID_PASSWORD":
				return "Incorrect password. Please try again.";
			case "USER_DISABLED":
				return "This account has been disabled.";
			case "TOO_MANY_ATTEMPTS_TRY_LATER":
				return "Too many failed attempts. Please try again later.";
			case "INVALID_EMAIL":
				return "Please enter a valid email address.";
			case "WEAK_PASSWORD":
				return "Password must be at least 6 characters long.";
			case "EMAIL_EXISTS":
				return "An account with this email already exists.";
			default:
				return "Authentication failed. Please try again.";
		}
	}
}
